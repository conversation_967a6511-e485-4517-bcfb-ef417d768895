"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Languages, Globe, Check, RotateCcw, X, Eye } from "lucide-react"
import { NewsletterBuilderData } from "@/types/newsletter"
import { HTMLPreview } from "./html-preview"

interface TranslatePreviewDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  translatedNewsletter: NewsletterBuilderData | null
  targetLanguage: string // This is actually the base language used for translation
  originalPrompt: string // This is actually the context provided
  onReplace: () => void
  onRedo: () => void
  onCancel: () => void
}

export function TranslatePreviewDialog({
  open,
  onOpenChange,
  translatedNewsletter,
  targetLanguage,
  originalPrompt,
  onReplace,
  onRedo,
  onCancel
}: TranslatePreviewDialogProps) {
  const getLanguageDisplay = (langCode: string) => {
    const languageMap: Record<string, { display: string; color: string }> = {
      'es': { display: 'Espanyol', color: 'text-red-500' },
      'ca': { display: 'Català', color: 'text-yellow-500' },
      'fr': { display: 'Francès', color: 'text-blue-500' },
      'en': { display: 'Anglès', color: 'text-green-500' }
    }
    return languageMap[langCode] || { display: langCode.toUpperCase(), color: 'text-gray-500' }
  }

  const handleReplace = () => {
    onReplace()
    onOpenChange(false)
  }

  const handleRedo = () => {
    onRedo()
    onOpenChange(false)
  }

  const handleCancel = () => {
    onCancel()
    onOpenChange(false)
  }

  if (!translatedNewsletter) {
    return null
  }

  const { display: languageDisplay, color: languageColor } = getLanguageDisplay(targetLanguage)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5 text-blue-600" />
            Previsualització de la Traducció
          </DialogTitle>
          <DialogDescription>
            Revisa el contingut traduït abans d'aplicar els canvis a la newsletter.
          </DialogDescription>
        </DialogHeader>

        {/* <DialogContent className="overflow-hidden"> */}
        <div className="space-y-4">
          {/* Translation Info */}
          <div className="flex items-center gap-4 p-3 bg-muted rounded-lg">
            <div className="flex items-center gap-2">
              <Globe className={`h-4 w-4 ${languageColor}`} />
              <span className="font-medium">Traduït des de: {languageDisplay}</span>
            </div>
            {originalPrompt && (
              <div className="flex-1">
                <Badge variant="outline" className="text-xs">
                  Context: {originalPrompt.length > 50 ? `${originalPrompt.substring(0, 50)}...` : originalPrompt}
                </Badge>
              </div>
            )}
          </div>

          {/* Newsletter Preview */}
          <ScrollArea className="h-[400px] w-full border rounded-lg">
            <HTMLPreview
              blocks={translatedNewsletter.nl_blocks}
              headers={translatedNewsletter.headers}
              footers={translatedNewsletter.footers}
            />
          </ScrollArea>
        </div>
        {/* </DialogContent> */}

        <DialogFooter className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            color="destructive"
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Cancel·lar
          </Button>

          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleRedo}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Tornar a traduir
            </Button>
            <Button
              type="button"
              onClick={handleReplace}
              className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
            >
              <Check className="h-4 w-4" />
              Aplicar Traducció
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
